import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/auth/token_manager.dart';
import '../../../../core/error/failure.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';

@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final SharedPreferences sharedPreferences;
  final TokenManager tokenManager;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.sharedPreferences,
    required this.tokenManager,
  });

  @override
  Future<Either<Failure, AuthUser>> login(String email, String password) async {
    try {
      final response = await remoteDataSource.login({
        'email': email,
        'password': password,
      });

      final authUser = response.toEntity();

      // Save tokens securely using TokenManager
      if (authUser.accessToken != null &&
          authUser.refreshToken != null &&
          authUser.tokenExpiry != null) {
        await tokenManager.saveAuthData(
          accessToken: authUser.accessToken!,
          refreshToken: authUser.refreshToken!,
          tokenExpiry: authUser.tokenExpiry!,
          userId: authUser.id,
        );
      }

      return Right(authUser);
    } on DioException catch (e) {
      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Login failed',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> sendSmsCode({required String phone}) async {
    try {
      await remoteDataSource.sendSmsCode({
        'phone': phone,
      });
      return const Right(null);
    } on DioException catch (e) {
      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Failed to send SMS code',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUser>> smsLogin({required String phone, required String code}) async {
    try {
      final response = await remoteDataSource.smsLogin({
        'phone': phone,
        'code': code,
      });

      final authUser = response.toEntity();

      // Save tokens securely using TokenManager
      if (authUser.accessToken != null &&
          authUser.refreshToken != null &&
          authUser.tokenExpiry != null) {
        await tokenManager.saveAuthData(
          accessToken: authUser.accessToken!,
          refreshToken: authUser.refreshToken!,
          tokenExpiry: authUser.tokenExpiry!,
          userId: authUser.id,
        );
      }

      return Right(authUser);
    } on DioException catch (e) {
      // Mock authentication - accept any phone number and 6-digit code
      if (phone.isNotEmpty && code.length == 6) {
        final mockUser = AuthUser(
          id: 'mock_user_123',
          email: '$<EMAIL>',
          name: '模拟用户',
          phone: phone,
          avatar: null,
          accessToken: 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
          refreshToken: 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
          tokenExpiry: DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
        );

        // Save mock tokens securely using TokenManager
        await tokenManager.saveAuthData(
          accessToken: mockUser.accessToken!,
          refreshToken: mockUser.refreshToken!,
          tokenExpiry: mockUser.tokenExpiry!,
          userId: mockUser.id,
        );

        if (kDebugMode) { print('AuthRepository: Mock SMS login successful for phone: $phone'); }
        return Right(mockUser);
      }

      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'SMS login failed',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUser>> wechatLogin() async {
    try {
      final response = await remoteDataSource.wechatLogin();

      final authUser = response.toEntity();

      // Save tokens securely using TokenManager
      if (authUser.accessToken != null &&
          authUser.refreshToken != null &&
          authUser.tokenExpiry != null) {
        await tokenManager.saveAuthData(
          accessToken: authUser.accessToken!,
          refreshToken: authUser.refreshToken!,
          tokenExpiry: authUser.tokenExpiry!,
          userId: authUser.id,
        );
      }

      return Right(authUser);
    } on DioException {
      // Mock WeChat login - always succeed for demo purposes
      final mockWechatUser = AuthUser(
        id: 'mock_wechat_user_${DateTime.now().millisecondsSinceEpoch}',
        email: '<EMAIL>',
        name: '微信用户',
        phone: null,
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/mock_avatar.png',
        accessToken: 'mock_wechat_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'mock_wechat_refresh_${DateTime.now().millisecondsSinceEpoch}',
        tokenExpiry: DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
      );

      // Save mock tokens securely using TokenManager
      await tokenManager.saveAuthData(
        accessToken: mockWechatUser.accessToken!,
        refreshToken: mockWechatUser.refreshToken!,
        tokenExpiry: mockWechatUser.tokenExpiry!,
        userId: mockWechatUser.id,
      );

      if (kDebugMode) { print('AuthRepository: Mock WeChat login successful'); }
      return Right(mockWechatUser);
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      final token = await tokenManager.getAccessToken();
      if (token != null) {
        await remoteDataSource.logout('Bearer $token');
      }

      // Clear all auth data securely
      await tokenManager.clearAuthData();

      return const Right(null);
    } on DioException catch (e) {
      // Even if server logout fails, clear local data
      await tokenManager.clearAuthData();

      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Logout failed',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      // Even if unexpected error, clear local data
      await tokenManager.clearAuthData();

      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUser?>> getCurrentUser() async {
    try {
      final token = await tokenManager.getAccessToken();
      final tokenExpiry = await tokenManager.getTokenExpiry();
      final userId = await tokenManager.getUserId();

      if (token == null) {
        return const Left(ServerFailure(message: 'No access token found'));
      }

      // Check if token is expired
      if (_isTokenExpired(tokenExpiry)) {
        await tokenManager.clearAuthData();
        return const Left(ServerFailure(message: 'Token expired'));
      }

      try {
        final response = await remoteDataSource.getCurrentUser('Bearer $token');
        final authUser = response.toEntity();
        return Right(authUser);
      } on DioException catch (e) {
        // If server request fails, try to return mock user based on stored data
        if (userId == 'mock_user_123') {
          final refreshToken = await tokenManager.getRefreshToken();
          final mockUser = AuthUser(
            id: 'mock_user_123',
            email: '<EMAIL>',
            name: '模拟用户',
            phone: '13800138000',
            avatar: null,
            accessToken: token,
            refreshToken: refreshToken,
            tokenExpiry: tokenExpiry,
          );
          return Right(mockUser);
        } else if (userId?.startsWith('mock_wechat_user_') == true) {
          final refreshToken = await tokenManager.getRefreshToken();
          final mockWechatUser = AuthUser(
            id: userId!,
            email: '<EMAIL>',
            name: '微信用户',
            phone: null,
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/mock_avatar.png',
            accessToken: token,
            refreshToken: refreshToken,
            tokenExpiry: tokenExpiry,
          );
          return Right(mockWechatUser);
        }

        return Left(ServerFailure(
          message: e.response?.data?['message'] ?? 'Failed to get current user',
          code: e.response?.statusCode,
        ));
      }
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> refreshToken() async {
    try {
      final refreshToken = await tokenManager.getRefreshToken();
      if (refreshToken == null) {
        return const Left(ServerFailure(message: 'No refresh token found'));
      }

      final response = await remoteDataSource.refreshToken({
        'refresh_token': refreshToken,
      });

      final authUser = response.toEntity();

      // Save new tokens securely using TokenManager
      if (authUser.accessToken != null &&
          authUser.refreshToken != null &&
          authUser.tokenExpiry != null) {
        await tokenManager.updateTokens(
          accessToken: authUser.accessToken,
          refreshToken: authUser.refreshToken,
          tokenExpiry: authUser.tokenExpiry,
        );
      }

      return const Right(null);
    } on DioException catch (e) {
      // If refresh fails, clear auth data
      await tokenManager.clearAuthData();

      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Token refresh failed',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> register(String email, String password, String name) async {
    try {
      await remoteDataSource.register({
        'email': email,
        'password': password,
        'name': name,
      });
      return const Right(null);
    } on DioException catch (e) {
      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Registration failed',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    try {
      await remoteDataSource.forgotPassword({
        'email': email,
      });
      return const Right(null);
    } on DioException catch (e) {
      return Left(ServerFailure(
        message: e.response?.data?['message'] ?? 'Failed to send reset email',
        code: e.response?.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final hasValidData = await tokenManager.hasValidAuthData();
      return Right(hasValidData);
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  bool _isTokenExpired(String? tokenExpiry) {
    if (tokenExpiry == null) return false;

    try {
      final expiryDate = DateTime.parse(tokenExpiry);
      final now = DateTime.now();
      return now.isAfter(expiryDate);
    } catch (e) {
      return true; // 解析失败，认为过期
    }
  }


}
